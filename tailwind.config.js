/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          blue: '#0066FF',
          pink: '#FF0066',
        },
        accent: {
          yellow: '#FFFF00',
          green: '#00FF66',
        },
        brutal: {
          black: '#000000',
          white: '#FFFFFF',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'brutal': '8px 8px 0px #000000',
        'brutal-small': '4px 4px 0px #000000',
      },
      animation: {
        'bounce-slow': 'bounce 3s infinite',
      },
      transform: {
        'asymmetric': 'rotate(-0.5deg)',
        'anti-asymmetric': 'rotate(0.3deg)',
      }
    },
  },
  plugins: [],
}
