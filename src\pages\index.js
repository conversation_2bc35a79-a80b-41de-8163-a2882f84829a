// Placeholder pages for basic functionality
import React from "react";
import { Button } from "@/components/ui/button";

const createPlaceholderPage = (title, description, bgColor = "bg-blue-500") => {
  return function PlaceholderPage() {
    return (
      <div className="bg-white">
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <div className={`${bgColor} text-white p-6 brutal-border brutal-shadow inline-block asymmetric-grid`}>
                <h1 className="brutal-text text-3xl md:text-4xl">{title}</h1>
              </div>
              <p className="text-xl font-bold mt-6 max-w-3xl mx-auto">
                {description}
              </p>
            </div>
            
            <div className="text-center">
              <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                COMING SOON
              </Button>
            </div>
          </div>
        </section>
      </div>
    );
  };
};

export const ServiceDetail = createPlaceholderPage(
  "SERVICE DETAILS", 
  "Detailed information about our social media marketing services.",
  "bg-green-400"
);

export const Process = createPlaceholderPage(
  "OUR PROCESS", 
  "Learn about our proven 4-step process for social media success.",
  "bg-yellow-400"
);

export const CaseStudies = createPlaceholderPage(
  "CASE STUDIES", 
  "Real results from real clients. See how we've helped brands grow.",
  "bg-purple-500"
);

export const CaseStudyDetail = createPlaceholderPage(
  "CASE STUDY", 
  "Detailed case study showing our results and methodology.",
  "bg-purple-500"
);

export const Testimonials = createPlaceholderPage(
  "TESTIMONIALS", 
  "What our clients say about working with GrowthLab.",
  "bg-green-500"
);

export const Resources = createPlaceholderPage(
  "FREE RESOURCES", 
  "Download our free guides, templates, and tools to grow your social media.",
  "bg-orange-500"
);

export const Tools = createPlaceholderPage(
  "FREE TOOLS", 
  "Use our free social media tools to audit and optimize your presence.",
  "bg-red-500"
);

export const Blog = createPlaceholderPage(
  "BLOG", 
  "Latest insights, tips, and strategies for social media marketing.",
  "bg-indigo-500"
);

export const BlogPost = createPlaceholderPage(
  "BLOG POST", 
  "In-depth article about social media marketing strategies.",
  "bg-indigo-500"
);

export const FAQ = createPlaceholderPage(
  "FAQ", 
  "Frequently asked questions about our services and process.",
  "bg-gray-500"
);

export const PrivacyPolicy = createPlaceholderPage(
  "PRIVACY POLICY", 
  "How we collect, use, and protect your personal information.",
  "bg-gray-600"
);

export const Terms = createPlaceholderPage(
  "TERMS OF SERVICE", 
  "Terms and conditions for using our website and services.",
  "bg-gray-600"
);
