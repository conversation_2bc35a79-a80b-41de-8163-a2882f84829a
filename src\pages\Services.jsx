import React from "react";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { But<PERSON> } from "@/components/ui/button";

export default function Services() {
  return (
    <div className="bg-white">
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow inline-block asymmetric-grid">
              <h1 className="brutal-text text-3xl md:text-4xl">OUR SERVICES</h1>
            </div>
            <p className="text-xl font-bold mt-6 max-w-3xl mx-auto">
              Comprehensive social media marketing services designed to generate real business results.
            </p>
          </div>
          
          <div className="text-center">
            <Link to={createPageUrl("Contact")}>
              <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                GET STARTED
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
