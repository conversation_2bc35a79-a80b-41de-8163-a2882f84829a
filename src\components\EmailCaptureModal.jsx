import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { X, Send, Download, Loader2, CheckCircle } from 'lucide-react';
import { Resource } from '@/entities';

// Mock SendEmail function for now - replace with actual integration
const SendEmail = async ({ to, subject, body, from_name }) => {
  console.log('Sending email:', { to, subject, body, from_name });
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  return { success: true };
};

export default function EmailCaptureModal({ resource, isOpen, onClose }) {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email || !name) {
        alert("Please provide your name and email.");
        return;
    }
    setIsSubmitting(true);

    try {
      // Send a thank you email with the download link
      await SendEmail({
        to: email,
        subject: `Here is your resource: ${resource.title}`,
        body: `Hi ${name},\n\nThanks for your interest in our resources! You can download "${resource.title}" using the link below:\n\n${resource.file_url}\n\nIf you're ready to get serious about your social media growth, book a free strategy call with us.\n\nBest,\nThe GrowthLab Team`,
        from_name: "GrowthLab SMMA"
      });

      // Update download count
      await Resource.update(resource.id, { download_count: (resource.download_count || 0) + 1 });
      
      setIsSubmitted(true);

      // Trigger download for the user
      window.open(resource.file_url, '_blank');

    } catch (error) {
      console.error('Error submitting email or downloading resource:', error);
      alert('There was an error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleClose = () => {
      setEmail('');
      setName('');
      setIsSubmitted(false);
      onClose();
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <div className="bg-white brutal-border brutal-shadow p-8 rounded-none max-w-lg w-full relative transform-gpu asymmetric-grid">
        <button onClick={handleClose} className="absolute -top-4 -right-4 bg-pink-500 text-white p-2 brutal-border brutal-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none">
          <X className="w-6 h-6" />
        </button>

        {!isSubmitted ? (
          <>
            <h2 className="brutal-text text-2xl mb-2">GET YOUR FREE RESOURCE</h2>
            <p className="font-bold mb-6">Enter your email below to get instant access to <span className="text-pink-500">"{resource.title}"</span>.</p>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="brutal-text text-sm mb-2 block">YOUR NAME</label>
                <Input
                  type="text"
                  placeholder="Enter your name..."
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="brutal-border brutal-shadow-small"
                  required
                />
              </div>
              <div>
                <label className="brutal-text text-sm mb-2 block">YOUR EMAIL</label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="brutal-border brutal-shadow-small"
                  required
                />
              </div>
              <Button type="submit" disabled={isSubmitting} className="w-full bg-blue-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none disabled:opacity-50">
                {isSubmitting ? (
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> SENDING...</>
                ) : (
                  <><Download className="mr-2 h-4 w-4" /> DOWNLOAD NOW</>
                )}
              </Button>
            </form>
          </>
        ) : (
          <div className="text-center">
            <div className="bg-green-400 w-20 h-20 brutal-border brutal-shadow mx-auto mb-6 flex items-center justify-center">
                <CheckCircle className="w-10 h-10 text-black" />
            </div>
            <h2 className="brutal-text text-2xl mb-4">SUCCESS!</h2>
            <p className="font-bold mb-4">Your download has started. We've also sent a copy to <span className="text-blue-500">{email}</span>.</p>
            <Button onClick={handleClose} className="bg-black text-white brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none">
                CLOSE
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
