/**
 * Creates a URL for a page based on the page name
 * @param {string} pageName - The name of the page
 * @returns {string} The URL path for the page
 */
export function createPageUrl(pageName) {
  // Handle special cases
  if (pageName === "Home") {
    return "/";
  }
  
  // Convert page names to URL-friendly format
  const urlMap = {
    "About": "/about",
    "Services": "/services", 
    "Process": "/process",
    "CaseStudies": "/case-studies",
    "Testimonials": "/testimonials",
    "Resources": "/resources",
    "Tools": "/tools",
    "Blog": "/blog",
    "FAQ": "/faq",
    "Contact": "/contact",
    "PrivacyPolicy": "/privacy-policy",
    "Terms": "/terms",
    "ServiceDetail": "/services/:service",
    "CaseStudyDetail": "/case-studies/:id",
    "BlogPost": "/blog/:slug"
  };
  
  return urlMap[pageName] || `/${pageName.toLowerCase()}`;
}

/**
 * Formats a date string for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date
 */
export function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Truncates text to a specified length
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} Truncated text
 */
export function truncateText(text, maxLength = 150) {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}
