import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';

// Import pages
import Home from './pages/Home';
import About from './pages/About';
import Services from './pages/Services';
import Contact from './pages/Contact';
import {
  ServiceDetail,
  Process,
  CaseStudies,
  CaseStudyDetail,
  Testimonials,
  Resources,
  Tools,
  Blog,
  BlogPost,
  FAQ,
  PrivacyPolicy,
  Terms
} from './pages';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout currentPageName="Home"><Home /></Layout>} />
        <Route path="/about" element={<Layout currentPageName="About"><About /></Layout>} />
        <Route path="/services" element={<Layout currentPageName="Services"><Services /></Layout>} />
        <Route path="/services/:service" element={<Layout currentPageName="ServiceDetail"><ServiceDetail /></Layout>} />
        <Route path="/process" element={<Layout currentPageName="Process"><Process /></Layout>} />
        <Route path="/case-studies" element={<Layout currentPageName="CaseStudies"><CaseStudies /></Layout>} />
        <Route path="/case-studies/:id" element={<Layout currentPageName="CaseStudyDetail"><CaseStudyDetail /></Layout>} />
        <Route path="/testimonials" element={<Layout currentPageName="Testimonials"><Testimonials /></Layout>} />
        <Route path="/resources" element={<Layout currentPageName="Resources"><Resources /></Layout>} />
        <Route path="/tools" element={<Layout currentPageName="Tools"><Tools /></Layout>} />
        <Route path="/blog" element={<Layout currentPageName="Blog"><Blog /></Layout>} />
        <Route path="/blog/:slug" element={<Layout currentPageName="BlogPost"><BlogPost /></Layout>} />
        <Route path="/faq" element={<Layout currentPageName="FAQ"><FAQ /></Layout>} />
        <Route path="/contact" element={<Layout currentPageName="Contact"><Contact /></Layout>} />
        <Route path="/privacy-policy" element={<Layout currentPageName="PrivacyPolicy"><PrivacyPolicy /></Layout>} />
        <Route path="/terms" element={<Layout currentPageName="Terms"><Terms /></Layout>} />
      </Routes>
    </Router>
  );
}

export default App;
