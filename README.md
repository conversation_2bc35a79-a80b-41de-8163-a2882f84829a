# GrowthLab SMMA Website

A modern, brutalist-design social media marketing agency website built with V<PERSON>, React, and Tailwind CSS.

## 🚀 Tech Stack

- **Vite** - Fast build tool and development server
- **React 18** - Modern React with hooks
- **React Router** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icons
- **PostCSS** - CSS processing

## 📁 Project Structure

```
├── public/                 # Static assets
│   ├── vite.svg           # Vite logo
│   └── .gitkeep           # Keep directory in git
├── src/                   # Source code
│   ├── components/        # React components
│   │   ├── ui/           # Reusable UI components
│   │   │   ├── button.jsx
│   │   │   ├── input.jsx
│   │   │   ├── select.jsx
│   │   │   ├── slider.jsx
│   │   │   └── textarea.jsx
│   │   ├── EmailCaptureModal.jsx
│   │   ├── Layout.jsx     # Main layout component
│   │   └── TrustElements.jsx
│   ├── pages/            # Page components
│   │   ├── Home.jsx      # Homepage
│   │   ├── About.jsx     # About page
│   │   ├── Services.jsx  # Services page
│   │   ├── Contact.jsx   # Contact page
│   │   └── index.js      # Placeholder pages
│   ├── entities/         # Data models and mock data
│   │   └── index.js      # Entity classes
│   ├── styles/           # CSS files
│   │   └── index.css     # Main CSS with Tailwind
│   ├── utils/            # Utility functions
│   │   └── index.js      # Helper functions
│   ├── App.jsx           # Main app component
│   └── main.jsx          # App entry point
├── index.html            # HTML template
├── package.json          # Dependencies and scripts
├── vite.config.js        # Vite configuration
├── tailwind.config.js    # Tailwind configuration
├── postcss.config.js     # PostCSS configuration
├── .eslintrc.cjs         # ESLint configuration
└── .gitignore            # Git ignore rules
```

## 🎨 Design System

The website uses a **brutalist design** approach with:

- **Bold, uppercase typography** (brutal-text class)
- **Thick black borders** (brutal-border class)
- **Drop shadows** (brutal-shadow classes)
- **Asymmetric layouts** (asymmetric-grid, anti-asymmetric classes)
- **Bright, contrasting colors**

### Color Palette

- **Primary Blue**: #0066FF
- **Primary Pink**: #FF0066  
- **Accent Yellow**: #FFFF00
- **Accent Green**: #00FF66
- **Brutal Black**: #000000
- **Brutal White**: #FFFFFF

## 🛠️ Development

### Prerequisites

- Node.js 16+ 
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🔧 Configuration

### Path Aliases

The project uses `@` as an alias for the `src` directory:

```javascript
import { Button } from '@/components/ui/button';
import { createPageUrl } from '@/utils';
```

### Tailwind CSS

Custom utilities and components are defined in `src/styles/index.css`:

- `.brutal-border` - 4px black border
- `.brutal-shadow` - 8px black drop shadow
- `.brutal-text` - Bold, uppercase text
- `.asymmetric-grid` - Slight rotation for visual interest

## 📱 Features

- **Responsive design** - Mobile-first approach
- **Modern React patterns** - Hooks, functional components
- **Fast development** - Hot module replacement with Vite
- **Type-safe routing** - React Router v6
- **Optimized builds** - Tree shaking and code splitting
- **SEO friendly** - Proper meta tags and semantic HTML

## 🚀 Deployment

The project builds to static files and can be deployed to any static hosting service:

- Vercel
- Netlify  
- GitHub Pages
- AWS S3 + CloudFront

## 📄 License

This project is proprietary and confidential.
