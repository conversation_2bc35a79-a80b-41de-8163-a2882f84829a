import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

export function Select({ children, onValueChange, value, ...props }) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value || '');
  const selectRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleValueChange = (newValue) => {
    setSelectedValue(newValue);
    setIsOpen(false);
    if (onValueChange) {
      onValueChange(newValue);
    }
  };

  return (
    <div ref={selectRef} className="relative">
      {React.Children.map(children, child => {
        if (child.type === SelectTrigger) {
          return React.cloneElement(child, {
            onClick: () => setIsOpen(!isOpen),
            isOpen,
            selectedValue
          });
        }
        if (child.type === SelectContent) {
          return React.cloneElement(child, {
            isOpen,
            onValueChange: handleValueChange
          });
        }
        return child;
      })}
    </div>
  );
}

export function SelectTrigger({ children, className = '', onClick, isOpen, selectedValue }) {
  return (
    <button
      type="button"
      className={`w-full px-3 py-2 border border-gray-300 bg-white text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-150 flex items-center justify-between ${className}`}
      onClick={onClick}
    >
      <span className={selectedValue ? 'text-gray-900' : 'text-gray-500'}>
        {children}
      </span>
      <ChevronDown className={`w-4 h-4 transition-transform duration-150 ${isOpen ? 'rotate-180' : ''}`} />
    </button>
  );
}

export function SelectValue({ placeholder }) {
  return <span>{placeholder}</span>;
}

export function SelectContent({ children, isOpen, onValueChange, className = '' }) {
  if (!isOpen) return null;

  return (
    <div className={`absolute top-full left-0 right-0 bg-white border border-gray-300 border-t-0 max-h-60 overflow-auto z-50 ${className}`}>
      {React.Children.map(children, child => {
        if (child.type === SelectItem) {
          return React.cloneElement(child, { onValueChange });
        }
        return child;
      })}
    </div>
  );
}

export function SelectItem({ children, value, onValueChange, className = '' }) {
  return (
    <button
      type="button"
      className={`w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none transition-colors duration-150 ${className}`}
      onClick={() => onValueChange(value)}
    >
      {children}
    </button>
  );
}
