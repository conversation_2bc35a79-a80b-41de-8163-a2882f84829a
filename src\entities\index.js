// Mock data and entity classes for the application
// In a real application, these would connect to a backend API

class BaseEntity {
  static data = [];
  
  static async list(sortBy = 'created_date') {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    let sorted = [...this.data];
    if (sortBy.startsWith('-')) {
      const field = sortBy.substring(1);
      sorted.sort((a, b) => new Date(b[field]) - new Date(a[field]));
    } else {
      sorted.sort((a, b) => new Date(a[sortBy]) - new Date(b[sortBy]));
    }
    
    return sorted;
  }
  
  static async filter(criteria, sortBy = 'created_date') {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    let filtered = this.data.filter(item => {
      return Object.entries(criteria).every(([key, value]) => item[key] === value);
    });
    
    if (sortBy.startsWith('-')) {
      const field = sortBy.substring(1);
      filtered.sort((a, b) => new Date(b[field]) - new Date(a[field]));
    } else {
      filtered.sort((a, b) => new Date(a[sortBy]) - new Date(b[sortBy]));
    }
    
    return filtered;
  }
  
  static async findById(id) {
    await new Promise(resolve => setTimeout(resolve, 100));
    return this.data.find(item => item.id === id);
  }
  
  static async update(id, updates) {
    await new Promise(resolve => setTimeout(resolve, 100));
    const index = this.data.findIndex(item => item.id === id);
    if (index !== -1) {
      this.data[index] = { ...this.data[index], ...updates };
      return this.data[index];
    }
    return null;
  }
}

export class BlogPost extends BaseEntity {
  static data = [
    {
      id: '1',
      title: '10 Social Media Trends That Will Dominate 2024',
      slug: '10-social-media-trends-2024',
      excerpt: 'Stay ahead of the curve with these emerging social media trends that will shape your marketing strategy in 2024.',
      content: 'Full blog post content here...',
      featured_image_url: '/images/blog/social-media-trends-2024.jpg',
      category: 'strategy',
      tags: ['trends', 'social media', '2024', 'strategy'],
      read_time: 8,
      published: true,
      created_date: '2024-01-15T10:00:00Z',
      updated_date: '2024-01-15T10:00:00Z'
    }
  ];
}

export class CaseStudy extends BaseEntity {
  static data = [
    {
      id: '1',
      client_name: 'TechFlow Solutions',
      client_logo_url: '/images/clients/techflow-logo.png',
      industry: 'Technology',
      challenge: 'Low social media engagement and poor lead generation from social channels.',
      solution: 'Implemented a comprehensive content strategy with targeted paid advertising campaigns.',
      results: [
        { metric: 'Engagement Rate', value: '8.5%', improvement: '+340%' },
        { metric: 'Lead Generation', value: '150/month', improvement: '+280%' },
        { metric: 'ROAS', value: '4.2x', improvement: '+180%' }
      ],
      timeline: '6 months',
      featured_image_url: '/images/case-studies/techflow-featured.jpg',
      created_date: '2024-01-10T10:00:00Z'
    }
  ];
}

export class Resource extends BaseEntity {
  static data = [
    {
      id: '1',
      title: 'Social Media Content Calendar Template',
      description: 'Plan your content strategy with our comprehensive monthly calendar template.',
      type: 'template',
      file_url: '/downloads/content-calendar-template.pdf',
      thumbnail_url: '/images/resources/content-calendar-thumb.jpg',
      download_count: 1247,
      category: 'content',
      featured: true,
      created_date: '2024-01-05T10:00:00Z'
    }
  ];
}

export class TeamMember extends BaseEntity {
  static data = [
    {
      id: '1',
      name: 'Sarah Johnson',
      role: 'Founder & CEO',
      bio: 'Former Facebook marketing manager with 8+ years of experience scaling brands through social media.',
      photo_url: '/images/team/sarah-johnson.jpg',
      email: '<EMAIL>',
      linkedin_url: 'https://linkedin.com/in/sarahjohnson',
      specialties: ['Strategy', 'Paid Advertising', 'Team Leadership'],
      order_index: 1
    }
  ];
}

export class Testimonial extends BaseEntity {
  static data = [
    {
      id: '1',
      client_name: 'Mike Chen',
      client_company: 'FitLife Gym Chain',
      client_role: 'Marketing Director',
      client_photo_url: '/images/testimonials/mike-chen.jpg',
      testimonial_text: 'GrowthLab transformed our social media presence. We went from 500 followers to 50K in just 8 months, and our membership sales increased by 200%.',
      rating: 5,
      service_used: 'Full Social Media Management',
      video_url: '/videos/testimonials/mike-chen-testimonial.mp4',
      featured: true,
      created_date: '2024-01-08T10:00:00Z'
    }
  ];
}
